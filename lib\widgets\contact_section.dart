import 'package:flutter/material.dart';
import 'package:my_portfolio/constants/colors.dart';
import 'package:my_portfolio/constants/size.dart';
import 'package:my_portfolio/constants/sns_links.dart';
import 'package:my_portfolio/widgets/custom_text_field.dart';
import 'dart:js' as js;

class ContactSection extends StatelessWidget {
  const ContactSection({super.key});

  @override
  Widget build(BuildContext context) {
    return  Container(
      padding: const EdgeInsets.fromLTRB(25, 20, 25, 60),
      color: CustomColor.bgLight1,
      child: Column(
        children: [
          Text(
            'Get in touch',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 24,
              color: CustomColor.whitePrimary,
            ),
          ),

          const SizedBox(height: 50),

          ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 700, maxHeight: 100,),
            child: LayoutBuilder(builder: (context, constraints) {
              if (constraints.maxWidth>=kMinDesktopWidth){
                return buildNameEmailFieldDesktop();
              }
              return buildNameEmailFieldMobile();
            })
          ),
          const SizedBox(height: 15),

          ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 700),
            child: CustomTextField(
              hintText: 'Your message',
              maxlines: 16,
            ),
          ),
          const SizedBox(height: 20),

          ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 700),
            child: SizedBox(
              width: double.maxFinite,
              child: ElevatedButton(
                onPressed: () {},
                child: Text('Get in touch'),
              ),
            ),
          ),

          ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 300),
            child: const Divider(),
          ),
          const SizedBox(height: 15),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            alignment: WrapAlignment.center,
            children: [
              InkWell(
                onTap: () {
                  js.context.callMethod('open', [SnsLinks.github]);
                },
                child: Image.asset('github.png', width: 28),
              ),
              InkWell(
                onTap: () {
                  js.context.callMethod('open', [SnsLinks.linkedIn]);
                },
                child: Image.asset('linkedin.png', width: 28),
              ),
              InkWell(
                onTap: () {
                  js.context.callMethod('open', [SnsLinks.facebook]);
                },
                child: Image.asset('facebook.png', width: 28),
              ),
              InkWell(
                onTap: () {
                  js.context.callMethod('open', [SnsLinks.instagram]);

                },
                child: Image.asset('instagram.png', width: 28),
              ),
              InkWell(
                onTap: () {
                  js.context.callMethod('open', [SnsLinks.telegram]);
                },
                child: Image.asset('telegram.png', width: 28),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Row buildNameEmailFieldDesktop(){
    return Row(
      children: [
        Flexible(
          child: CustomTextField(hintText: 'Your name'),
        ),
        const SizedBox(width: 15),

        Flexible(
          child: CustomTextField(hintText: 'Your email'),
        ),
      ],
    );
  }

  Column buildNameEmailFieldMobile(){
    return Column(
      children: [
        Flexible(
          child: CustomTextField(hintText: 'Your name'),
        ),
        const SizedBox(height: 15),

        Flexible(
          child: CustomTextField(hintText: 'Your email'),
        ),
      ],
    );
  }
}
